using Castle.DynamicProxy;
using Core.CrossCuttingConcerns.Logging;
using Core.CrossCuttingConcerns.Logging.FileLogger;
using Core.Utilities.Interceptors;
using Core.Utilities.IoC;
using Microsoft.Extensions.DependencyInjection;
using System.Diagnostics;

namespace Core.Aspects.Autofac.Performance
{
    public class PerformanceAspect : MethodInterception
    {
        private readonly int _interval;
        private readonly Stopwatch _stopwatch;
        private readonly ILogService _performanceLoggerService;

        public PerformanceAspect(int interval)
        {
            _interval = interval;
            _stopwatch = ServiceTool.ServiceProvider.GetService<Stopwatch>();
            _performanceLoggerService = ServiceTool.ServiceProvider.GetService<PerformanceLoggerService>();
        }

        protected override void OnBefore(IInvocation invocation)
        {
            _stopwatch.Start();
        }

        protected override void OnAfter(IInvocation invocation)
        {
            if (_stopwatch.Elapsed.TotalSeconds > _interval)
            {
                var logMessage = $"Performance: {invocation.Method.DeclaringType.FullName}.{invocation.Method.Name} took {_stopwatch.Elapsed.TotalSeconds} seconds";
                _performanceLoggerService.LogPerformance(logMessage);
            }
            _stopwatch.Reset();
        }
    }
}